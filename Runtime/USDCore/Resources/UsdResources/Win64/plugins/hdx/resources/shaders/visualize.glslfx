-- glslfx version 0.1

//
// Copyright 2021 Pixar
//
// Licensed under the Apache License, Version 2.0 (the "Apache License")
// with the following modification; you may not use this file except in
// compliance with the Apache License and the following modification to it:
// Section 6. Trademarks. is deleted and replaced with:
//
// 6. Trademarks. This License does not grant permission to use the trade
//    names, trademarks, service marks, or product names of the Licensor
//    and its affiliates, except as required to comply with Section 4(c) of
//    the License and to reproduce the content of the NOTICE file.
//
// You may obtain a copy of the Apache License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the Apache License with the above modification is
// distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied. See the Apache License for the specific
// language governing permissions and limitations under the Apache License.
//

-- configuration
{
    "techniques": {
        "default": {
            "VisualizeVertex": {
                "source": [ "Visualize.Vertex" ]
            },
            "VisualizeFragmentDepth": {
                "source": [ "Visualize.Fragment.Depth" ]
            },
            "VisualizeFragmentId": {
                "source": [ "Visualize.Fragment.Id" ]
            },
            "VisualizeFragmentNormal": {
                "source": [ "Visualize.Fragment.Normal" ]
            },
            "VisualizeFragmentFallback": {
                "source": [ "Visualize.Fragment.Fallback" ]
            }
        }
    }
}

-- glsl Visualize.Vertex

void main(void)
{
    gl_Position = position;
    uvOut = uvIn;
}

-- glsl Visualize.Fragment.Depth

// Re-normalize clip space depth in the range [0.0, 1.0] to the range [min, max]
// to allow better visualization of depth differences.
float normalizeDepth(float depth)
{
    float min = minMaxDepth.x, max = minMaxDepth.y;
    return (depth - min) / (max - min);
}

// Display the renormalized depth as a grayscale image.
void main(void)
{
    vec2 fragCoord = uvOut * screenSize;
    float depth = HgiTexelFetch_depthIn(ivec2(fragCoord)).x;
     hd_FragColor = vec4( vec3(normalizeDepth(depth)), 1.0 );
}

-- glsl Visualize.Fragment.Id

// Convert a 32 bit integer into a vec3 color.
vec3 IntToVec3(int id)
{
    // Create a 24 bit value by XORing the leading 8 bits with the remaining
    // 24 bits.
    int leadBits = id >> 24;
    int restBits = (id << 8) >> 8;
    int result = restBits ^ leadBits;

    return vec3(((result >>  0) & 0xff) / 255.0,
                ((result >>  8) & 0xff) / 255.0,
                ((result >> 16) & 0xff) / 255.0);
}

// Convert a 32 bit integer representing the ID of a primitive or sub-primitive
// into a color such that consecutive IDs generally map to different colors.
void main(void)
{
    vec2 fragCoord = uvOut * screenSize;
    int id = int(HgiTexelFetch_idIn(ivec2(fragCoord)).x);
    int vizId = id * 11629091; // prime number near ln(2) * 2^24
    hd_FragColor = vec4(IntToVec3(vizId), 1.0);
}

-- glsl Visualize.Fragment.Normal

// [-1,1] to [0,1]
vec3 renormalize(vec3 normal)
{
    return 0.5 * normal + 0.5;
}

void main(void)
{
    vec2 fragCoord = uvOut * screenSize;
    vec3 normal = HgiTexelFetch_normalIn(ivec2(fragCoord)).xyz;
    hd_FragColor = vec4(renormalize(normal), 1.0);
}

-- glsl Visualize.Fragment.Fallback

void main(void)
{
    vec2 fragCoord = uvOut * screenSize;
    // Force conversion to a vector of floats.
    hd_FragColor = vec4(HgiTexelFetch_aovIn(ivec2(fragCoord)));
}
