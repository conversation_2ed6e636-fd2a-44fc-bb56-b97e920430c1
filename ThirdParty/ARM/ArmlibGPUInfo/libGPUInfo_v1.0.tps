<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Software Name: libGPUInfo  </Name>
<!-- Software Name and Version  -->
<!-- Software Name: Software Name: libGPUInfo
    Version: 1.0 -->
    <EndUserGroup>Git</EndUserGroup>
  <Location>//UE5/Main/Engine/Source/ThirdParty/ARM/ArmlibGPUInfo/</Location>
  <Function>Retrieves information about ARM Mali GPUs needed to identify capabilities </Function>
  <Eula>https://github.com/ARM-software/libGPUInfo/blob/main/LICENSE.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder></LicenseFolder>
</TpsData>



