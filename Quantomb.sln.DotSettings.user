<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003APlugins_003A_002E_002E_003F_002E_002E_003FPlugins_002Fd_003AQuantombComputeShaders_002Fd_003ASource_002Fd_003AQuantombComputeShaders_002Fd_003APublic_002Ff_003AQuantombComputeShaders_002Eh/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Fd_003AFiniteStateMachine_002Ff_003AFiniteStateMachine_002Ecpp/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Fd_003AFiniteStateMachine_002Ff_003AFiniteStateMachine_002Eh/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Fd_003AFiniteStateMachine_002Ff_003AFSMState_002Ecpp/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Fd_003AFiniteStateMachine_002Ff_003AFSMState_002Eh/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Fd_003AFiniteStateMachine_002Ff_003AFSMTransition_002Ecpp/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Fd_003AFiniteStateMachine_002Ff_003AFSMTransition_002Eh/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Fd_003APlayer_002Ff_003AQuantombPlayer_002Ecpp/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Fd_003APlayer_002Ff_003AQuantombPlayer_002Eh/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Fd_003AProceduralWeaponAnimation_002Ff_003AProceduralWeaponAnimation_002Ecpp/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Fd_003AProceduralWeaponAnimation_002Ff_003AProceduralWeaponAnimation_002Eh/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Ff_003ACameraBrightnessActor_002Ecpp/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=5E72006B_002DDCE7_002D34E8_002D8E3B_002D18FB7AF4313A_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003FSource_002Fd_003AQuantomb_002Ff_003ACameraBrightnessActor_002Eh/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ABiwamuk_002Easm_002Fl_003AC_0021_003FUsers_003Fmail_003FAppData_003FLocal_003FTemp_003FSandboxFiles_003FBaqybyr_003FBiwamuk_002Easm/@EntryIndexedValue">ForceIncluded</s:String>
	<s:Int64 x:Key="/Default/Environment/Hierarchy/Build/BuildTool/MsbuildVersion/@EntryValue">1114112</s:Int64>
	<s:String x:Key="/Default/Environment/Hierarchy/Build/BuildTool/CustomBuildToolPath/@EntryValue">C:\Program Files\JetBrains\JetBrains Rider 2024.3\tools\MSBuild\Current\Bin\MSBuild.exe</s:String></wpf:ResourceDictionary>